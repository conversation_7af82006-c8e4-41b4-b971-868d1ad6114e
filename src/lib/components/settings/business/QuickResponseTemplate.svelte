<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import {
		PlusOutline,
		TrashBinSolid,
		EditOutline,
		CheckOutline,
		EyeOutline
	} from 'flowbite-svelte-icons';

	import { onMount } from 'svelte';
	import {
		templates as templatesStore,
		templatesLoading,
		templatesError,
		templateActions
	} from '$lib/stores/quickResponseTemplates';
	import {
		Accordion,
		AccordionItem,
		Button,
		Input,
		Label,
		Textarea,
		Modal,
		Alert
	} from 'flowbite-svelte';

	// TypeScript interfaces
	interface QuickResponseTemplate {
		id: string;
		keyword: string;
		template: string;
		description: string;
		created_at?: string;
		updated_at?: string;
	}

	interface ValidationErrors {
		keyword?: string;
		template?: string;
		description?: string;
		general?: string;
	}

	// Store reactive variables
	$: templates = $templatesStore;
	$: isLoading = $templatesLoading;
	$: storeError = $templatesError;

	// State management variables
	let isAddingTemplate = false;
	let templateToEdit: string | null = null;
	let templateToDelete: string | null = null;
	let isSubmitting = false;
	let showPreview = false;
	let previewTemplate: QuickResponseTemplate | null = null;

	// Form data
	let newTemplate: Partial<QuickResponseTemplate> = {
		keyword: '',
		template: '',
		description: ''
	};
	let editTemplate: Partial<QuickResponseTemplate> = {};

	// Validation and error handling
	let validationErrors: ValidationErrors = {};
	let formErrors: string | null = null;

	// Search and filter
	let searchQuery = '';
	let filteredTemplates: QuickResponseTemplate[] = [];

	// Reactive statements
	$: filteredTemplates = templates.filter(
		(template) =>
			template.keyword.toLowerCase().includes(searchQuery.toLowerCase()) ||
			template.template.toLowerCase().includes(searchQuery.toLowerCase()) ||
			template.description.toLowerCase().includes(searchQuery.toLowerCase())
	);

	// Validation functions
	function validateTemplate(template: Partial<QuickResponseTemplate>): ValidationErrors {
		const errors: ValidationErrors = {};

		if (!template.keyword?.trim()) {
			errors.keyword = t('template_keyword_required');
		} else if (template.keyword.trim().length < 2) {
			errors.keyword = t('template_keyword_min_length');
		} else if (template.keyword.trim().length > 20) {
			errors.keyword = t('template_keyword_max_length');
		}

		if (!template.template?.trim()) {
			errors.template = t('template_content_required');
		} else if (template.template.trim().length < 10) {
			errors.template = t('template_content_min_length');
		} else if (template.template.trim().length > 500) {
			errors.template = t('template_content_max_length');
		}

		if (!template.description?.trim()) {
			errors.description = t('template_description_required');
		} else if (template.description.trim().length > 100) {
			errors.description = t('template_description_max_length');
		}

		return errors;
	}

	function checkKeywordUniqueness(keyword: string, excludeId?: string): boolean {
		return !templates.some(
			(template) =>
				template.keyword.toLowerCase() === keyword.toLowerCase() && template.id !== excludeId
		);
	}

	// CRUD operation handlers
	function startAddingTemplate() {
		isAddingTemplate = true;
		newTemplate = { keyword: '', template: '', description: '' };
		validationErrors = {};
		formErrors = null;
	}

	function cancelAddingTemplate() {
		isAddingTemplate = false;
		newTemplate = { keyword: '', template: '', description: '' };
		validationErrors = {};
		formErrors = null;
	}

	function startEditingTemplate(template: QuickResponseTemplate) {
		templateToEdit = template.id;
		editTemplate = { ...template };
		validationErrors = {};
		formErrors = null;
	}

	function cancelEditingTemplate() {
		templateToEdit = null;
		editTemplate = {};
		validationErrors = {};
		formErrors = null;
	}

	function confirmDeleteTemplate(templateId: string) {
		templateToDelete = templateId;
	}

	function cancelDeleteTemplate() {
		templateToDelete = null;
	}

	function showTemplatePreview(template: QuickResponseTemplate) {
		previewTemplate = template;
		showPreview = true;
	}

	// Form submission handlers
	async function handleAddSubmit() {
		if (!validateBeforeSubmit(newTemplate)) return;

		isSubmitting = true;
		formErrors = null;

		try {
			const result = await templateActions.addTemplate({
				keyword: newTemplate.keyword!,
				template: newTemplate.template!,
				description: newTemplate.description!
			});

			if (result) {
				isAddingTemplate = false;
				newTemplate = { keyword: '', template: '', description: '' };
				validationErrors = {};
			} else {
				formErrors = t('template_add_error');
			}
		} catch (error) {
			formErrors = error instanceof Error ? error.message : t('template_add_error');
		} finally {
			isSubmitting = false;
		}
	}

	async function handleEditSubmit() {
		if (!validateBeforeSubmit(editTemplate, templateToEdit!)) return;

		isSubmitting = true;
		formErrors = null;

		try {
			const result = await templateActions.updateTemplate(templateToEdit!, {
				keyword: editTemplate.keyword!,
				template: editTemplate.template!,
				description: editTemplate.description!
			});

			if (result) {
				templateToEdit = null;
				editTemplate = {};
				validationErrors = {};
			} else {
				formErrors = t('template_edit_error');
			}
		} catch (error) {
			formErrors = error instanceof Error ? error.message : t('template_edit_error');
		} finally {
			isSubmitting = false;
		}
	}

	async function handleDeleteSubmit() {
		if (!templateToDelete) return;

		isSubmitting = true;
		formErrors = null;

		try {
			const success = await templateActions.deleteTemplate(templateToDelete);

			if (success) {
				templateToDelete = null;
			} else {
				formErrors = t('template_delete_error');
			}
		} catch (error) {
			formErrors = error instanceof Error ? error.message : t('template_delete_error');
		} finally {
			isSubmitting = false;
		}
	}

	// Client-side validation before submission
	function validateBeforeSubmit(
		template: Partial<QuickResponseTemplate>,
		excludeId?: string
	): boolean {
		const errors = validateTemplate(template);

		// if (template.keyword && !checkKeywordUniqueness(template.keyword, excludeId)) {
		// 	errors.keyword = t('template_keyword_duplicate');
		// }

		validationErrors = errors;
		return Object.keys(errors).length === 0;
	}

	// Initialize component
	onMount(async () => {
		// Load templates when component mounts
		await templateActions.loadTemplates();
	});
</script>

<div class="space-y-4 rounded-lg bg-white p-6 shadow-md">
	<Accordion flush>
		<AccordionItem open>
			<span slot="header" class="text-lg font-medium text-gray-900">
				{t('quick_response_templates')}
			</span>

			<div class="space-y-4">
				<!-- Header with search and add button -->
				<div class="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
					<div class="max-w-md flex-1">
						<Label for="search-templates" class="sr-only">{t('search_templates')}</Label>
						<Input
							id="search-templates"
							type="text"
							placeholder={t('search_templates')}
							bind:value={searchQuery}
							class="w-full"
							aria-describedby="search-help"
						/>
						<div id="search-help" class="sr-only">Search templates by keyword or description</div>
					</div>

					{#if !isAddingTemplate}
						<Button
							color="blue"
							size="sm"
							on:click={startAddingTemplate}
							class="flex items-center gap-2"
						>
							<PlusOutline class="h-4 w-4" />
							{t('add_template')}
						</Button>
					{/if}
				</div>

				<!-- Error display -->
				{#if formErrors}
					<Alert color="red" class="mb-4">
						{formErrors}
					</Alert>
				{/if}

				{#if storeError}
					<Alert color="red" class="mb-4">
						{storeError}
					</Alert>
				{/if}

				<!-- Add new template form -->
				{#if isAddingTemplate}
					<div class="rounded-lg border border-gray-200 bg-gray-50 p-4">
						<form on:submit|preventDefault={handleAddSubmit} class="space-y-4">
							<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
								<div>
									<Label for="new-keyword" class="mb-2">{t('template_keyword')}</Label>
									<Input
										id="new-keyword"
										name="keyword"
										type="text"
										placeholder={t('template_keyword_placeholder')}
										bind:value={newTemplate.keyword}
										class={validationErrors.keyword ? 'border-red-500' : ''}
										required
									/>
									{#if validationErrors.keyword}
										<p class="mt-1 text-sm text-red-500">{validationErrors.keyword}</p>
									{/if}
								</div>

								<div>
									<Label for="new-description" class="mb-2">{t('template_description')}</Label>
									<Input
										id="new-description"
										name="description"
										type="text"
										placeholder={t('template_description_placeholder')}
										bind:value={newTemplate.description}
										class={validationErrors.description ? 'border-red-500' : ''}
										required
									/>
									{#if validationErrors.description}
										<p class="mt-1 text-sm text-red-500">{validationErrors.description}</p>
									{/if}
								</div>
							</div>

							<div>
								<Label for="new-template" class="mb-2">{t('template_content')}</Label>
								<Textarea
									id="new-template"
									name="template"
									placeholder={t('template_content_placeholder')}
									bind:value={newTemplate.template}
									rows={3}
									class={validationErrors.template ? 'border-red-500' : ''}
									required
								/>
								{#if validationErrors.template}
									<p class="mt-1 text-sm text-red-500">{validationErrors.template}</p>
								{/if}
							</div>

							<div class="flex gap-2">
								<Button
									type="submit"
									color="green"
									size="sm"
									disabled={isSubmitting}
									class="flex items-center gap-2"
								>
									<CheckOutline class="h-4 w-4" />
									{isSubmitting ? t('adding') : t('add_template')}
								</Button>
								<Button
									type="button"
									color="light"
									size="sm"
									on:click={cancelAddingTemplate}
									disabled={isSubmitting}
								>
									{t('cancel')}
								</Button>
							</div>
						</form>
					</div>
				{/if}

				<!-- Templates list -->
				<div class="space-y-2" role="region" aria-label="Templates list">
					<!-- Live region for screen readers -->
					<div aria-live="polite" aria-atomic="true" class="sr-only">
						{#if isLoading}
							Loading templates...
						{:else if filteredTemplates.length === 0}
							No templates found
						{:else}
							{filteredTemplates.length} template{filteredTemplates.length === 1 ? '' : 's'} found
						{/if}
					</div>

					{#if isLoading}
						<div class="py-8 text-center text-gray-500">
							<div
								class="inline-block h-6 w-6 animate-spin rounded-full border-[3px] border-current border-t-transparent text-blue-600"
								role="status"
								aria-label="loading"
							>
								<span class="sr-only">Loading...</span>
							</div>
							<p class="mt-2">{t('loading_templates')}</p>
						</div>
					{:else if filteredTemplates.length === 0}
						<div class="py-8 text-center text-gray-500">
							{#if searchQuery}
								{t('no_templates_found')}
							{:else}
								{t('no_templates_yet')}
							{/if}
						</div>
					{:else}
						{#each filteredTemplates as template (template.id)}
							<div
								class="rounded-lg border border-gray-200 p-4 transition-colors focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 hover:bg-gray-50"
								role="article"
								aria-labelledby="template-title-{template.id}"
							>
								{#if templateToEdit === template.id}
									<!-- Edit form -->
									<form on:submit|preventDefault={handleEditSubmit} class="space-y-4">
										<input type="hidden" name="template_id" value={template.id} />

										<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
											<div>
												<Label for="edit-keyword-{template.id}" class="mb-2"
													>{t('template_keyword')}</Label
												>
												<Input
													id="edit-keyword-{template.id}"
													name="keyword"
													type="text"
													bind:value={editTemplate.keyword}
													class={validationErrors.keyword ? 'border-red-500' : ''}
													required
												/>
												{#if validationErrors.keyword}
													<p class="mt-1 text-sm text-red-500">{validationErrors.keyword}</p>
												{/if}
											</div>

											<div>
												<Label for="edit-description-{template.id}" class="mb-2"
													>{t('template_description')}</Label
												>
												<Input
													id="edit-description-{template.id}"
													name="description"
													type="text"
													bind:value={editTemplate.description}
													class={validationErrors.description ? 'border-red-500' : ''}
													required
												/>
												{#if validationErrors.description}
													<p class="mt-1 text-sm text-red-500">{validationErrors.description}</p>
												{/if}
											</div>
										</div>

										<div>
											<Label for="edit-template-{template.id}" class="mb-2"
												>{t('template_content')}</Label
											>
											<Textarea
												id="edit-template-{template.id}"
												name="template"
												bind:value={editTemplate.template}
												rows={3}
												class={validationErrors.template ? 'border-red-500' : ''}
												required
											/>
											{#if validationErrors.template}
												<p class="mt-1 text-sm text-red-500">{validationErrors.template}</p>
											{/if}
										</div>

										<div class="flex gap-2">
											<Button
												type="submit"
												color="green"
												size="sm"
												disabled={isSubmitting}
												class="flex items-center gap-2"
											>
												<CheckOutline class="h-4 w-4" />
												{isSubmitting ? t('updating') : t('update')}
											</Button>
											<Button
												type="button"
												color="light"
												size="sm"
												on:click={cancelEditingTemplate}
												disabled={isSubmitting}
											>
												{t('cancel')}
											</Button>
										</div>
									</form>
								{:else}
									<!-- Display template -->
									<div class="flex items-start justify-between">
										<div class="flex-1 space-y-2">
											<div class="flex items-center gap-3">
												<span
													id="template-title-{template.id}"
													class="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800"
												>
													{template.keyword}
												</span>
												<span class="text-sm text-gray-600">{template.description}</span>
											</div>
											<div class="rounded bg-gray-100 p-2 text-sm text-gray-900">
												{template.template}
											</div>
										</div>

										<div class="ml-4 flex items-center gap-2">
											{#if templateToDelete === template.id}
												<div class="flex gap-2">
													<form on:submit|preventDefault={handleDeleteSubmit} class="inline">
														<input type="hidden" name="template_id" value={template.id} />
														<Button type="submit" color="red" size="xs" disabled={isSubmitting}>
															{isSubmitting ? t('deleting') : t('delete')}
														</Button>
													</form>
													<Button
														type="button"
														color="light"
														size="xs"
														on:click={cancelDeleteTemplate}
														disabled={isSubmitting}
													>
														{t('cancel')}
													</Button>
												</div>
											{:else}
											<div class="flex flex-col gap-2 w-full">
												<!-- <Button
													type="button"
													color="light"
													size="xs"
													on:click={() => showTemplatePreview(template)}
													class="flex items-center gap-1"
												>
													<EyeOutline class="h-3 w-3" />
													{t('preview')}
												</Button> -->
												<Button
													type="button"
													color="light"
													size="xs"
													on:click={() => startEditingTemplate(template)}
													class="flex items-center gap-1"
												>
													<EditOutline class="h-3 w-3" />
													{t('edit')}
												</Button>
												<Button
													type="button"
													color="red"
													size="xs"
													on:click={() => confirmDeleteTemplate(template.id)}
													class="flex items-center gap-1"
												>
													<TrashBinSolid class="h-3 w-3" />
													{t('delete')}
												</Button>
											</div>
											{/if}
										</div>
									</div>
								{/if}
							</div>
						{/each}
					{/if}
				</div>
			</div>
		</AccordionItem>
	</Accordion>
</div>

<!-- Preview Modal -->
<Modal bind:open={showPreview} size="md" title={t('template_preview')}>
	<svelte:fragment slot="header">
		<h3 class="text-lg font-semibold">{t('template_preview')}</h3>
	</svelte:fragment>

	{#if previewTemplate}
		<div class="space-y-4">
			<div>
				<Label class="mb-2 block">{t('template_keyword')}</Label>
				<div class="rounded bg-gray-100 p-2 text-sm">
					{previewTemplate.keyword}
				</div>
			</div>

			<div>
				<Label class="mb-2 block">{t('template_description')}</Label>
				<div class="rounded bg-gray-100 p-2 text-sm">
					{previewTemplate.description}
				</div>
			</div>

			<div>
				<Label class="mb-2 block">{t('template_content')}</Label>
				<div class="whitespace-pre-wrap rounded bg-gray-100 p-3 text-sm">
					{previewTemplate.template}
				</div>
			</div>

			<div class="border-t pt-4">
				<Label class="mb-2 block">{t('autocomplete_preview')}</Label>
				<div class="rounded-lg border border-gray-200 bg-white p-3 shadow-sm">
					<div class="mb-2 text-xs text-gray-500">{t('how_it_appears_in_autocomplete')}</div>
					<div class="flex items-center gap-3 rounded p-2 hover:bg-gray-50">
						<span
							class="inline-flex items-center rounded bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800"
						>
							{previewTemplate.keyword}
						</span>
						<div class="flex-1">
							<div class="line-clamp-1 text-sm font-medium text-gray-900">
								{previewTemplate.template.substring(0, 60)}{previewTemplate.template.length > 60
									? '...'
									: ''}
							</div>
							<div class="text-xs text-gray-500">
								{previewTemplate.description}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	{/if}

	<svelte:fragment slot="footer">
		<Button color="light" on:click={() => (showPreview = false)}>
			{t('close')}
		</Button>
	</svelte:fragment>
</Modal>

<style>
	.line-clamp-1 {
		display: -webkit-box;
		-webkit-line-clamp: 1;
		line-clamp: 1;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
